import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import * as vscode from 'vscode'
import { VsCodeLmHandler } from '../vscode-lm'
import type { ApiHandlerOptions } from '../../../shared/api'

// Mock VSCode API
vi.mock('vscode', () => ({
	LanguageModelChatMessage: {
		Assistant: vi.fn((content: string) => ({ role: 'assistant', content })),
		User: vi.fn((content: string) => ({ role: 'user', content })),
	},
	LanguageModelTextPart: class {
		constructor(public value: string) {}
	},
	LanguageModelToolCallPart: class {
		constructor(public callId: string, public name: string, public input: any) {}
	},
	LanguageModelToolResult: class {
		constructor(public content: any[]) {}
	},
	CancellationTokenSource: class {
		token = { isCancellationRequested: false }
		cancel() {}
		dispose() {}
	},
	CancellationError: class extends Error {
		constructor() {
			super('Cancelled')
		}
	},
	workspace: {
		onDidChangeConfiguration: vi.fn(() => ({ dispose: vi.fn() })),
	},
	lm: {
		selectChatModels: vi.fn(() => Promise.resolve([])),
		invokeTool: vi.fn(),
	},
}))

describe('VsCodeLmHandler Multi-Turn Tool Calling', () => {
	let handler: VsCodeLmHandler
	let mockOptions: ApiHandlerOptions

	beforeEach(() => {
		mockOptions = {
			vsCodeLmModelSelector: {
				vendor: 'copilot',
				family: 'gpt-4',
			},
		} as ApiHandlerOptions

		// Reset all mocks
		vi.clearAllMocks()
	})

	afterEach(() => {
		if (handler) {
			handler.dispose()
		}
	})

	it('should initialize with multi-turn tool calling enabled', () => {
		handler = new VsCodeLmHandler(mockOptions)
		expect(handler).toBeDefined()
		// The constructor should log the configuration
	})

	it('should handle single message without tool calls', async () => {
		handler = new VsCodeLmHandler(mockOptions)
		
		// Mock the client and response
		const mockClient = {
			name: 'test-model',
			vendor: 'test-vendor',
			sendRequest: vi.fn().mockResolvedValue({
				stream: (async function* () {
					yield new vscode.LanguageModelTextPart('Hello, world!')
				})(),
			}),
		}

		// Mock getClient to return our mock client
		vi.spyOn(handler as any, 'getClient').mockResolvedValue(mockClient)
		vi.spyOn(handler as any, 'calculateTotalInputTokens').mockResolvedValue(10)
		vi.spyOn(handler as any, 'internalCountTokens').mockResolvedValue(5)

		const systemPrompt = 'You are a helpful assistant.'
		const messages = [
			{
				role: 'user' as const,
				content: 'Hello!',
			},
		]

		const stream = handler.createMessage(systemPrompt, messages)
		const chunks = []

		for await (const chunk of stream) {
			chunks.push(chunk)
		}

		expect(chunks).toHaveLength(2) // text chunk + usage chunk
		expect(chunks[0]).toEqual({
			type: 'text',
			text: 'Hello, world!',
		})
		expect(chunks[1]).toEqual({
			type: 'usage',
			inputTokens: 10,
			outputTokens: 5,
		})
	})

	it('should handle multi-turn tool calling', async () => {
		handler = new VsCodeLmHandler(mockOptions)
		
		// Mock tool invocation
		const mockToolResult = {
			content: [new vscode.LanguageModelTextPart('Tool result content')],
		}
		vi.mocked(vscode.lm.invokeTool).mockResolvedValue(mockToolResult)

		// Mock the client with tool calls
		const mockClient = {
			name: 'test-model',
			vendor: 'test-vendor',
			sendRequest: vi.fn()
				.mockResolvedValueOnce({
					// First round: assistant makes a tool call
					stream: (async function* () {
						yield new vscode.LanguageModelTextPart('I need to use a tool.')
						yield new vscode.LanguageModelToolCallPart('call-1', 'test-tool', { query: 'test' })
					})(),
				})
				.mockResolvedValueOnce({
					// Second round: assistant responds with final answer
					stream: (async function* () {
						yield new vscode.LanguageModelTextPart('Based on the tool result, here is my answer.')
					})(),
				}),
		}

		// Mock getClient to return our mock client
		vi.spyOn(handler as any, 'getClient').mockResolvedValue(mockClient)
		vi.spyOn(handler as any, 'calculateTotalInputTokens').mockResolvedValue(10)
		vi.spyOn(handler as any, 'internalCountTokens').mockResolvedValue(5)

		const systemPrompt = 'You are a helpful assistant.'
		const messages = [
			{
				role: 'user' as const,
				content: 'Please help me with something that requires a tool.',
			},
		]

		const stream = handler.createMessage(systemPrompt, messages)
		const chunks = []

		for await (const chunk of stream) {
			chunks.push(chunk)
		}

		// Should have multiple text chunks and one usage chunk
		expect(chunks.length).toBeGreaterThan(2)
		
		// Check that tool was invoked
		expect(vscode.lm.invokeTool).toHaveBeenCalledWith(
			'test-tool',
			expect.objectContaining({
				input: { query: 'test' },
				toolInvocationToken: undefined,
			}),
			expect.any(Object)
		)

		// Check that client.sendRequest was called twice (multi-turn)
		expect(mockClient.sendRequest).toHaveBeenCalledTimes(2)

		// Final chunk should be usage
		const lastChunk = chunks[chunks.length - 1]
		expect(lastChunk.type).toBe('usage')
	})

	it('should respect maximum rounds limit', async () => {
		handler = new VsCodeLmHandler(mockOptions)
		
		// Mock tool invocation
		const mockToolResult = {
			content: [new vscode.LanguageModelTextPart('Tool result')],
		}
		vi.mocked(vscode.lm.invokeTool).mockResolvedValue(mockToolResult)

		// Mock the client to always return tool calls (infinite loop scenario)
		const mockClient = {
			name: 'test-model',
			vendor: 'test-vendor',
			sendRequest: vi.fn().mockResolvedValue({
				stream: (async function* () {
					yield new vscode.LanguageModelTextPart('Making another tool call.')
					yield new vscode.LanguageModelToolCallPart('call-' + Date.now(), 'test-tool', { query: 'test' })
				})(),
			}),
		}

		// Mock getClient to return our mock client
		vi.spyOn(handler as any, 'getClient').mockResolvedValue(mockClient)
		vi.spyOn(handler as any, 'calculateTotalInputTokens').mockResolvedValue(10)
		vi.spyOn(handler as any, 'internalCountTokens').mockResolvedValue(5)

		const systemPrompt = 'You are a helpful assistant.'
		const messages = [
			{
				role: 'user' as const,
				content: 'Start an infinite tool calling loop.',
			},
		]

		const stream = handler.createMessage(systemPrompt, messages)
		const chunks = []

		for await (const chunk of stream) {
			chunks.push(chunk)
		}

		// Should stop at max rounds (10 by default)
		expect(mockClient.sendRequest).toHaveBeenCalledTimes(10)
	})
})
