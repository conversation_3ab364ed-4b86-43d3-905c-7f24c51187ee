/**
 * 优化的工具调用示例
 * 
 * 这个示例展示了正确实现的多轮工具调用优化：
 * 1. 每次 createMessage 调用只发起一次 VSCode LM 请求
 * 2. VSCode 的 LanguageModelToolCallPart 被正确转换为 RooCode 格式
 * 3. 多轮交互通过 RooCode 的 recursivelyMakeClineRequests 循环实现
 * 4. 实现了与 Copilot 相同的成本优化效果
 */

import { VsCodeLmHandler } from '../src/api/providers/vscode-lm'
import type { ApiHandlerOptions } from '../src/shared/api'

async function demonstrateOptimizedToolCalling() {
	console.log('=== 优化的工具调用示例 ===\n')

	// 初始化 VSCode LM 处理器
	const options: ApiHandlerOptions = {
		vsCodeLmModelSelector: {
			vendor: 'copilot',
			family: 'gpt-4',
		},
	}

	const handler = new VsCodeLmHandler(options)

	try {
		// 示例：复杂的多步骤任务
		console.log('场景：用户请求分析项目结构并生成文档')
		console.log('预期流程：')
		console.log('1. RooCode 调用 createMessage')
		console.log('2. VSCode LM 返回文本 + LanguageModelToolCallPart')
		console.log('3. 工具调用被转换为 RooCode JSON 格式')
		console.log('4. RooCode 执行工具并调用 recursivelyMakeClineRequests')
		console.log('5. 新的 createMessage 调用继续对话')
		console.log('6. 重复直到任务完成\n')

		const systemPrompt = `你是一个代码分析助手。当用户要求分析项目时，你应该：
1. 首先列出项目文件
2. 读取关键文件了解结构
3. 生成详细的分析报告`

		const messages = [
			{
				role: 'user' as const,
				content: '请分析这个项目的结构，重点关注 src 目录下的文件组织方式。',
			},
		]

		console.log('发起第一次 createMessage 调用...')
		const stream = handler.createMessage(systemPrompt, messages)

		let textContent = ''
		let hasToolCalls = false

		for await (const chunk of stream) {
			if (chunk.type === 'text') {
				textContent += chunk.text
				
				// 检查是否包含工具调用
				if (chunk.text.includes('"type":"tool_call"')) {
					hasToolCalls = true
					console.log('✅ 检测到工具调用被转换为 RooCode 格式')
				}
				
				// 实时显示部分内容
				process.stdout.write(chunk.text)
			} else if (chunk.type === 'usage') {
				console.log(`\n\n=== 第一轮完成 ===`)
				console.log(`输入 tokens: ${chunk.inputTokens}`)
				console.log(`输出 tokens: ${chunk.outputTokens}`)
				console.log(`包含工具调用: ${hasToolCalls ? '是' : '否'}`)
			}
		}

		if (hasToolCalls) {
			console.log('\n✅ 成功演示：')
			console.log('- 单次 createMessage 调用')
			console.log('- VSCode LM 工具调用被正确转换')
			console.log('- 为 RooCode 的多轮循环做好准备')
			console.log('- 成本优化：只计费一次 VSCode LM 请求')
		} else {
			console.log('\n📝 注意：这次响应没有工具调用')
			console.log('在实际使用中，RooCode 会根据需要继续多轮交互')
		}

		console.log('\n=== 成本对比 ===')
		console.log('传统方式（错误）：')
		console.log('- 每个工具调用 = 新的 sendRequest = 额外计费')
		console.log('- 3个工具调用 = 4次计费（1次初始 + 3次工具）')
		
		console.log('\n优化方式（正确）：')
		console.log('- 每个 createMessage = 1次 sendRequest = 1次计费')
		console.log('- 3轮交互 = 3次计费（每轮都是完整的 LM 交互）')
		console.log('- 与 Copilot 的成本模型一致')

	} catch (error) {
		console.error('演示过程中出错:', error)
	} finally {
		handler.dispose()
	}
}

// 解释实现细节
function explainImplementation() {
	console.log(`
=== 实现细节解释 ===

1. 单次请求原则：
   - 每个 createMessage() 调用只执行一次 client.sendRequest()
   - 避免了错误的多次请求导致的额外计费

2. 工具调用转换：
   - VSCode 的 LanguageModelToolCallPart 被转换为 JSON 格式
   - 格式：{"type":"tool_call","name":"...","arguments":{...},"callId":"..."}
   - 与 RooCode 现有的 parseAssistantMessage 兼容

3. 多轮循环：
   - 由 RooCode 的 recursivelyMakeClineRequests 处理
   - 每轮都是新的 createMessage 调用
   - 每轮都只计费一次

4. 架构兼容：
   - 完全保持 BaseProvider 接口
   - 不破坏现有的工具执行逻辑
   - 向后兼容所有现有功能

5. 性能优化：
   - 流式处理工具调用
   - 实时转换和输出
   - 减少内存使用

=== 与 Copilot 的对比 ===

特性                 | Copilot | RooCode 优化后
--------------------|---------|---------------
每轮请求数           | 1次     | 1次 ✅
工具调用处理         | 流式    | 流式 ✅  
多轮循环机制         | 内置    | 利用现有循环 ✅
成本优化            | 是      | 是 ✅
架构兼容性          | N/A     | 完全兼容 ✅

这个实现成功地将 Copilot 的成本优化策略应用到 RooCode 中，
同时保持了完全的架构兼容性。
`)
}

// 运行演示
if (require.main === module) {
	demonstrateOptimizedToolCalling()
		.then(() => {
			explainImplementation()
		})
		.catch(console.error)
}

export { demonstrateOptimizedToolCalling, explainImplementation }
