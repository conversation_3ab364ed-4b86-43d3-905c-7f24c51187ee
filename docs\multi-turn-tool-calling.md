# Multi-Turn Tool Calling in VSCode Language Model Provider

This document describes the multi-turn tool calling feature implemented in the VSCode Language Model provider, which allows for multiple rounds of tool calls within a single request, similar to GitHub Copilot's approach.

## Overview

The multi-turn tool calling feature enables the language model to:
1. Make tool calls in response to user queries
2. Receive tool results and continue the conversation
3. Make additional tool calls if needed
4. Provide a final response based on all tool interactions

This approach counts as only **one high-level request** for cost optimization, regardless of how many tool calling rounds occur.

## Key Features

### Cost Optimization
- Multiple tool calling rounds count as a single request
- Reduces API costs compared to separate requests for each tool call
- Maintains conversation context throughout the entire interaction

### Automatic Tool Execution
- Tools are automatically executed using VSCode's `vscode.lm.invokeTool` API
- Tool results are automatically fed back into the conversation
- No manual intervention required for tool execution

### Configurable Limits
- Maximum number of rounds: 10 (configurable)
- Automatic termination when no more tool calls are made
- Safety limits to prevent infinite loops

## How It Works

### 1. Initial Request
```typescript
const handler = new VsCodeLmHandler(options)
const stream = handler.createMessage(systemPrompt, messages)
```

### 2. Multi-Turn Loop
```
Round 1: User message → Assistant response with tool calls → Tool execution
Round 2: Tool results → Assistant response with more tool calls → Tool execution
Round 3: Tool results → Assistant final response (no tool calls) → End
```

### 3. Token Counting
- Input tokens are accumulated across all rounds
- Output tokens are accumulated across all rounds
- Final usage is reported at the end

## Configuration

The multi-turn feature is controlled by these properties in `VsCodeLmHandler`:

```typescript
private readonly maxToolCallRounds: number = 10
private readonly enableMultiTurnToolCalling: boolean = true
```

### Enabling/Disabling
- When `enableMultiTurnToolCalling` is `true`: Uses multi-turn loop
- When `enableMultiTurnToolCalling` is `false`: Falls back to single-turn behavior

### Round Limits
- `maxToolCallRounds` controls the maximum number of rounds
- Default is 10 rounds to prevent infinite loops
- Loop terminates early if no tool calls are made

## Example Usage

### Simple Tool Call
```typescript
const systemPrompt = "You are a helpful assistant with access to tools."
const messages = [{
  role: "user",
  content: "What's the weather like in New York?"
}]

// This might result in:
// Round 1: Assistant calls weather tool
// Round 2: Assistant provides weather report based on tool result
```

### Complex Multi-Tool Scenario
```typescript
const messages = [{
  role: "user", 
  content: "Find the latest commit in the main branch and create a summary report"
}]

// This might result in:
// Round 1: Assistant calls git tool to get latest commit
// Round 2: Assistant calls file reading tool to get commit details
// Round 3: Assistant calls report generation tool
// Round 4: Assistant provides final summary
```

## Error Handling

### Tool Execution Errors
- Individual tool failures don't stop the entire loop
- Error messages are fed back to the assistant
- Assistant can retry or use alternative approaches

### Cancellation
- Entire loop can be cancelled using cancellation tokens
- Partial results are preserved
- Clean state is maintained

### Timeout Protection
- Maximum rounds prevent infinite loops
- Automatic termination when no progress is made
- Graceful degradation to single-turn behavior

## Debugging

### Logging
The implementation includes comprehensive logging:

```typescript
console.debug("Roo Code <Language Model API>: Processing tool call:", {
  name: chunk.name,
  callId: chunk.callId,
  inputSize: JSON.stringify(chunk.input).length,
  round: roundCount,
})
```

### Configuration Logging
```typescript
console.log("Roo Code <Language Model API>: Multi-turn tool calling configuration:", {
  enabled: this.enableMultiTurnToolCalling,
  maxRounds: this.maxToolCallRounds,
})
```

## Comparison with Single-Turn

### Single-Turn (Traditional)
- One request → One response
- Tool calls require separate requests
- Higher cost for complex interactions
- Manual orchestration needed

### Multi-Turn (New)
- One request → Multiple internal rounds → One final response
- Tool calls handled automatically
- Lower cost for complex interactions
- Automatic orchestration

## Best Practices

### Tool Design
- Design tools to be stateless when possible
- Provide clear error messages for tool failures
- Keep tool execution time reasonable

### Prompt Engineering
- Include clear instructions about when to use tools
- Provide context about available tools
- Guide the assistant on when to stop calling tools

### Error Recovery
- Handle tool failures gracefully
- Provide fallback strategies
- Log errors for debugging

## Limitations

### Current Limitations
- Maximum 10 rounds by default
- No user intervention during tool calling loop
- All tools must be available through VSCode's tool API

### Future Enhancements
- Configurable round limits per request
- User confirmation for sensitive tool calls
- Streaming tool execution progress
- Tool call dependency analysis

## Integration with RooCode

This feature integrates seamlessly with RooCode's existing tool system:
- Uses RooCode's tool parsing and presentation logic
- Maintains compatibility with existing tool implementations
- Preserves RooCode's tool result formatting
- Works with RooCode's task management system

The multi-turn tool calling feature provides a more efficient and cost-effective way to handle complex interactions that require multiple tool calls, while maintaining full compatibility with RooCode's existing architecture.
