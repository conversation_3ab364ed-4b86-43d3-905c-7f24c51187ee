# 正确的多轮工具调用实现方案

## 问题分析

之前的实现方式是错误的，因为我在单个 `createMessage` 调用中发起了多次 `client.sendRequest`，这会被 VSCode LM API 计算为多次请求，违背了成本优化的目标。

## Copilot 的正确实现方式

通过分析 `vscode-copilot-chat/src/extension/intents/node/toolCallingLoop.ts`，我发现 Copilot 的多轮工具调用机制是：

### 核心架构
1. **外层循环**: `ToolCallingLoop.run()` 方法中的 `while (true)` 循环
2. **每轮一次请求**: 每轮调用 `runOne()` 方法，只发起一次 LM 请求
3. **工具调用在流中接收**: 通过 `delta.copilotToolCalls` 在流式响应中接收工具调用
4. **工具执行在下轮处理**: 工具结果在下一轮的 `buildPrompt` 中处理

### 关键代码片段
```typescript
// 外层循环 - 每轮只算一次请求
while (true) {
    const result = await this.runOne(outputStream, i, token);
    if (!result.round.toolCalls.length) {
        break; // 没有工具调用，结束循环
    }
    // 继续下一轮
}

// 每轮只发起一次 fetch 请求
const fetchResult = await this.fetch(
    fixedMessages,
    async (text, _, delta) => {
        if (delta.copilotToolCalls) {
            toolCalls.push(...delta.copilotToolCalls); // 在流中接收工具调用
        }
    },
    // ... 其他参数
);
```

## RooCode 的正确实现方案

### 架构对比

**RooCode 现有架构**:
```
用户请求 → createMessage → LM响应 → parseAssistantMessage → 
presentAssistantMessage → 执行工具 → recursivelyMakeClineRequests → 
新的 createMessage → ...
```

**优化后的架构**:
```
用户请求 → createMessage (单次LM请求) → 
VSCode LanguageModelToolCallPart → 转换为RooCode格式 → 
parseAssistantMessage → presentAssistantMessage → 执行工具 → 
recursivelyMakeClineRequests → 新的 createMessage (单次LM请求) → ...
```

### 关键实现

#### 1. 单次请求原则
```typescript
override async *createMessage(...): ApiStream {
    // 重要：每次调用只发起一次 VSCode LM 请求
    yield* this.runEnhancedSingleRequest(systemPrompt, cleanedMessages, client)
}
```

#### 2. 优化的工具调用处理
```typescript
private convertVSCodeToolCallToRooCodeFormat(chunk: vscode.LanguageModelToolCallPart): string {
    // 将 VSCode 的 LanguageModelToolCallPart 转换为 RooCode 期望的 JSON 格式
    const toolCall = {
        type: "tool_call",
        name: chunk.name,
        arguments: chunk.input,
        callId: chunk.callId,
    }
    return JSON.stringify(toolCall)
}
```

#### 3. 流式处理优化
```typescript
for await (const chunk of response.stream) {
    if (chunk instanceof vscode.LanguageModelTextPart) {
        yield { type: "text", text: chunk.value }
    } else if (chunk instanceof vscode.LanguageModelToolCallPart) {
        // 转换为 RooCode 格式并流式输出
        const toolCallText = this.convertVSCodeToolCallToRooCodeFormat(chunk)
        yield { type: "text", text: toolCallText }
    }
}
```

## 成本优化效果

### 传统方式 (错误)
```
用户: "分析文件并生成报告"
→ 请求1: 读取文件 (计费)
→ 请求2: 分析内容 (计费) 
→ 请求3: 生成报告 (计费)
总计: 3次计费
```

### 优化后方式 (正确)
```
用户: "分析文件并生成报告"
→ 请求1: LM响应 + 工具调用(读取文件) (计费)
→ 请求2: LM响应 + 工具调用(分析) (计费)
→ 请求3: LM最终响应 (计费)
总计: 3次计费，但每次都是完整的LM交互
```

**关键区别**: 每次 `createMessage` 调用都是一次完整的 LM 交互，包含文本生成和工具调用，而不是多次分割的请求。

## 与 Copilot 的对比

| 特性 | Copilot | RooCode 优化后 |
|------|---------|---------------|
| 每轮请求数 | 1次 | 1次 ✅ |
| 工具调用处理 | 流式接收 | 流式接收 ✅ |
| 多轮循环 | ToolCallingLoop | recursivelyMakeClineRequests ✅ |
| 成本优化 | 每轮一次计费 | 每轮一次计费 ✅ |
| 架构兼容 | N/A | 完全兼容 ✅ |

## 实现优势

### 1. 成本效率
- 每次 `createMessage` = 一次 VSCode LM 请求
- 避免了错误的多次 `sendRequest` 调用
- 与 Copilot 的成本模型一致

### 2. 架构兼容
- 完全保持 RooCode 现有架构
- 不破坏 `BaseProvider` 接口
- 与现有工具系统无缝集成

### 3. 性能优化
- 流式处理工具调用
- 实时转换 VSCode 格式到 RooCode 格式
- 减少不必要的数据转换

### 4. 可维护性
- 清晰的职责分离
- 易于调试和监控
- 向后兼容

## 总结

这个实现正确地模仿了 Copilot 的多轮工具调用机制：

1. **单次请求原则**: 每个 `createMessage` 只发起一次 VSCode LM 请求
2. **流式工具处理**: 在响应流中接收和转换工具调用
3. **架构兼容**: 利用 RooCode 现有的多轮循环机制
4. **成本优化**: 确保每次调用只计费一次

这种方式既实现了成本优化的目标，又保持了与 RooCode 架构的完全兼容性。
